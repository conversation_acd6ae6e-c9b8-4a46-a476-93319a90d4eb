# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

OpenCode is a terminal-based AI assistant for software development written in Go. It provides an interactive chat interface with AI capabilities, code analysis, and LSP integration to assist developers directly from the terminal.

## Architecture

The application follows a modular architecture:

- **cmd**: Command-line interface using Cobra
- **internal/app**: Core application services
- **internal/config**: Configuration management
- **internal/db**: Database operations and migrations (SQLite with SQLC)
- **internal/llm**: LLM providers and tools integration
- **internal/tui**: Terminal UI components using Bubble Tea
- **internal/logging**: Logging infrastructure
- **internal/message**: Message handling
- **internal/session**: Session management
- **internal/lsp**: Language Server Protocol integration

## Key Components

1. **Configuration System**: Multi-source configuration using Viper (environment variables, JSON files)
2. **Database**: SQLite database with migrations managed by <PERSON>, generated code with SQLC
3. **LLM Integration**: Support for multiple providers (Anthropic, OpenAI, Google, AWS Bedrock, Groq, Azure, etc.)
4. **TUI**: Terminal user interface built with Bubble Tea
5. **Tools System**: Extensible tool framework for AI agents (file operations, shell commands, etc.)
6. **MCP Support**: Model Context Protocol integration for external tools
7. **LSP Integration**: Language Server Protocol support for code intelligence

## Common Development Tasks

### Building the Project

```bash
# Build the binary
go build -o opencode

# Run the application
./opencode
```

### Running Tests

```bash
# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...
```

### Database Management

The project uses SQLC for database code generation and Goose for migrations:

```bash
# Generate database code (requires sqlc installed)
sqlc generate

# Create a new migration
goose -dir internal/db/migrations create migration_name sql
```

### Configuration Schema

The project includes a schema generator for configuration validation:

```bash
# Generate JSON schema
go run cmd/schema/main.go > opencode-schema.json
```

## Important Configuration Files

- `.opencode.json` - User configuration file
- `sqlc.yaml` - Database code generation configuration
- Database migrations in `internal/db/migrations/`

## Supported AI Providers

- Anthropic (Claude models)
- OpenAI (GPT models)
- Google Gemini
- AWS Bedrock
- Groq
- Azure OpenAI
- GitHub Copilot
- OpenRouter
- XAI

## Key Environment Variables

- `ANTHROPIC_API_KEY` - For Claude models
- `OPENAI_API_KEY` - For OpenAI models
- `GEMINI_API_KEY` - For Google Gemini models
- `GITHUB_TOKEN` - For Github Copilot models
- `GROQ_API_KEY` - For Groq models
- `AWS_ACCESS_KEY_ID`/`AWS_SECRET_ACCESS_KEY` - For AWS Bedrock
- `AZURE_OPENAI_ENDPOINT` - For Azure OpenAI models

## Testing Approach

Tests use the standard Go testing framework with testify for assertions. Test files are named with `_test.go` suffix and typically include:

- Unit tests for individual functions
- Integration tests for services
- Mock implementations for external dependencies

The project follows a pattern of creating temporary directories for file-based tests.